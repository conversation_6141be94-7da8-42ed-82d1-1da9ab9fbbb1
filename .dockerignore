# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
.venv/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Git
.git/
.gitignore
.github/

# Docker
Dockerfile
docker-compose.yml
docker-run.sh

# Database
data/
docs/
*.db
*.sqlite3
*.db-journal

# Logs
logs/
*.log

# Documentation
README.md
LICENSE

# Tests
tests/
test/
.pytest_cache/
