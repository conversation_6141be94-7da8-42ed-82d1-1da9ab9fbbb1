# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
.venv/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Database
data/
docs/
*.db
*.sqlite3
*.db-journal

# Logs
*.log
logs/

# Test files
test_*.py
tests/
.pytest_cache/

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
